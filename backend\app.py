#!/usr/bin/env python3
"""
Cube AI Solutions - Flask Automation Backend
AI-Powered Resume Screening System with Flask REST API

Author: Cube AI Solutions
Version: 2.0.0
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import traceback

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from flask_restful import Api, Resource
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager, jwt_required, create_access_token, get_jwt_identity
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash

# Import our existing AI agent
from cube_ai_solutions import CubeAIHRAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('flask_hr_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'cube-ai-hr-secret-key-2024')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///hr_automation.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'jwt-secret-string')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'

# Initialize extensions
db = SQLAlchemy(app)
migrate = Migrate(app, db)
jwt = JWTManager(app)
api = Api(app)
CORS(app)

# Create upload directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Database Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(50), default='hr_manager')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

class JobPosting(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=False)
    requirements = db.Column(db.Text)
    department = db.Column(db.String(100))
    location = db.Column(db.String(100))
    salary_range = db.Column(db.String(100))
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

class Candidate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    resume_path = db.Column(db.String(255))
    skills = db.Column(db.Text)  # JSON string
    experience_years = db.Column(db.Integer)
    education = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(50), default='new')

class AnalysisResult(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    candidate_id = db.Column(db.Integer, db.ForeignKey('candidate.id'))
    job_id = db.Column(db.Integer, db.ForeignKey('job_posting.id'))
    ai_score = db.Column(db.Float)
    match_percentage = db.Column(db.Float)
    strengths = db.Column(db.Text)  # JSON string
    weaknesses = db.Column(db.Text)  # JSON string
    recommendations = db.Column(db.Text)  # JSON string
    analysis_data = db.Column(db.Text)  # Full AI analysis JSON
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# Initialize AI Agent (will be done after app context)
ai_agent = None

def init_ai_agent():
    """Initialize the AI agent with proper configuration"""
    global ai_agent
    try:
        openai_key = os.environ.get('OPENAI_API_KEY')
        if not openai_key:
            logger.warning("⚠️ OpenAI API key not found. AI features will be limited.")
            return None
        
        credentials_path = os.environ.get('GOOGLE_CREDENTIALS_PATH', 'credentials.json')
        ai_agent = CubeAIHRAgent(openai_key, credentials_path)
        logger.info("✅ AI Agent initialized successfully")
        return ai_agent
    except Exception as e:
        logger.error(f"❌ Failed to initialize AI Agent: {str(e)}")
        return None

# API Resources
class AuthResource(Resource):
    def post(self):
        """User authentication endpoint"""
        try:
            data = request.get_json()
            email = data.get('email')
            password = data.get('password')
            
            if not email or not password:
                return {'error': 'Email and password required'}, 400
            
            # For demo purposes, use hardcoded credentials
            if email == '<EMAIL>' and password == 'password123':
                access_token = create_access_token(identity=email)
                return {
                    'access_token': access_token,
                    'user': {
                        'email': email,
                        'name': 'Admin User',
                        'role': 'admin'
                    }
                }, 200
            
            return {'error': 'Invalid credentials'}, 401
            
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return {'error': 'Authentication failed'}, 500

class ResumeUploadResource(Resource):
    @jwt_required()
    def post(self):
        """Handle resume file uploads"""
        try:
            if 'files' not in request.files:
                return {'error': 'No files provided'}, 400
            
            files = request.files.getlist('files')
            job_description = request.form.get('job_description', '')
            
            results = []
            
            for file in files:
                if file.filename == '':
                    continue
                
                if file and self._allowed_file(file.filename):
                    filename = secure_filename(file.filename)
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"{timestamp}_{filename}"
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    file.save(filepath)
                    
                    # Process with AI if available
                    analysis_result = self._analyze_resume(filepath, job_description)
                    
                    results.append({
                        'filename': file.filename,
                        'status': 'uploaded',
                        'analysis': analysis_result
                    })
            
            return {'results': results}, 200
            
        except Exception as e:
            logger.error(f"Upload error: {str(e)}")
            return {'error': 'Upload failed'}, 500
    
    def _allowed_file(self, filename):
        """Check if file extension is allowed"""
        ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx'}
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
    
    def _analyze_resume(self, filepath, job_description):
        """Analyze resume using AI agent"""
        try:
            if not ai_agent:
                return {'error': 'AI agent not available', 'mock_analysis': True}
            
            # Use the AI agent to analyze the resume
            analysis = ai_agent.analyze_single_resume(filepath, job_description)
            return analysis
            
        except Exception as e:
            logger.error(f"Analysis error: {str(e)}")
            # Return mock analysis for demo
            return {
                'mock_analysis': True,
                'ai_score': 85.5,
                'match_percentage': 78.2,
                'strengths': ['Strong technical skills', 'Relevant experience', 'Good education background'],
                'weaknesses': ['Limited leadership experience', 'Could improve communication skills'],
                'recommendations': ['Consider for technical interview', 'Assess leadership potential']
            }

class CandidatesResource(Resource):
    @jwt_required()
    def get(self):
        """Get all candidates"""
        try:
            candidates = Candidate.query.all()
            return {
                'candidates': [{
                    'id': c.id,
                    'name': c.name,
                    'email': c.email,
                    'phone': c.phone,
                    'experience_years': c.experience_years,
                    'status': c.status,
                    'created_at': c.created_at.isoformat()
                } for c in candidates]
            }, 200
        except Exception as e:
            logger.error(f"Get candidates error: {str(e)}")
            return {'error': 'Failed to fetch candidates'}, 500
    
    @jwt_required()
    def post(self):
        """Add new candidate"""
        try:
            data = request.get_json()
            
            candidate = Candidate(
                name=data.get('name'),
                email=data.get('email'),
                phone=data.get('phone'),
                experience_years=data.get('experience_years'),
                education=data.get('education'),
                skills=json.dumps(data.get('skills', []))
            )
            
            db.session.add(candidate)
            db.session.commit()
            
            return {'message': 'Candidate added successfully', 'id': candidate.id}, 201
            
        except Exception as e:
            logger.error(f"Add candidate error: {str(e)}")
            return {'error': 'Failed to add candidate'}, 500

class JobsResource(Resource):
    @jwt_required()
    def get(self):
        """Get all job postings"""
        try:
            jobs = JobPosting.query.filter_by(is_active=True).all()
            return {
                'jobs': [{
                    'id': j.id,
                    'title': j.title,
                    'description': j.description,
                    'department': j.department,
                    'location': j.location,
                    'salary_range': j.salary_range,
                    'created_at': j.created_at.isoformat()
                } for j in jobs]
            }, 200
        except Exception as e:
            logger.error(f"Get jobs error: {str(e)}")
            return {'error': 'Failed to fetch jobs'}, 500

# Register API routes
api.add_resource(AuthResource, '/api/auth/login')
api.add_resource(ResumeUploadResource, '/api/resumes/upload')
api.add_resource(CandidatesResource, '/api/candidates')
api.add_resource(JobsResource, '/api/jobs')

# Health check endpoint
@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'ai_agent_status': 'available' if ai_agent else 'unavailable'
    })

# API Info route
@app.route('/')
def api_info():
    """API information endpoint"""
    return jsonify({
        'name': 'Cube AI HR Automation Backend',
        'version': '2.0.0',
        'description': 'Flask-based AI-Powered Resume Screening System',
        'endpoints': {
            'health': '/api/health',
            'auth': '/api/auth/login',
            'upload': '/api/resumes/upload',
            'candidates': '/api/candidates',
            'jobs': '/api/jobs'
        },
        'features': [
            'AI-powered resume analysis',
            'Background task processing',
            'Database integration',
            'RESTful API',
            'Automated workflows'
        ],
        'status': 'running',
        'ai_agent_status': 'available' if ai_agent else 'demo_mode'
    })

# Initialize database and AI agent
def create_tables():
    """Create database tables and initialize AI agent"""
    with app.app_context():
        db.create_all()
        init_ai_agent()
        logger.info("🚀 Flask HR Automation Backend initialized successfully!")

if __name__ == '__main__':
    # Initialize on startup
    create_tables()
    # Development server
    app.run(debug=True, host='0.0.0.0', port=5000)
